"use client";
import Image from "next/image";
import Link from "next/link";
import { useState } from "react";
import { FaTelegram } from "react-icons/fa";
import { HiOutlineBars3BottomLeft } from "react-icons/hi2";
import { LiaTimesSolid } from "react-icons/lia";
import { AppButton } from "../../components/app-button";
import { Body3 } from "../../components/app-typography";

const PromoNavbar = () => {
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  const toggleMenu = () => {
    setIsMenuOpen(!isMenuOpen);
  };

  const closeMenu = () => {
    setIsMenuOpen(false);
  };

  const navLinks = [
    { href: "/", label: "Audits" },
    { href: "/pro", label: "Recon Pro" },
    { href: "/tools", label: "Tools" },
    {
      href: "https://marketplace.visualstudio.com/items?itemName=Recon-Fuzz.recon",
      label: "Extension",
      external: true,
    },
    { href: "https://book.getrecon.xyz/", label: "Book", external: true },
  ];

  const telegramBtn = (
    <Link
      href="https://t.me/GalloDaSballo"
      className="m-0 flex flex-row items-center justify-center p-0 text-center"
      target="_blank"
      rel="noopener noreferrer"
    >
      <AppButton variant="primary" size="default" rightIcon={<FaTelegram />}>
        Questions? Ask the Founder
      </AppButton>
    </Link>
  );

  const loginBtn = (
    <Link href="/dashboard" className="m-0 p-0 text-center">
      <AppButton variant="outline" size="default">
        Log in &gt;
      </AppButton>
    </Link>
  );

  return (
    <div className="fixed z-[1000] w-full">
      <div className="flex h-20 w-full items-center justify-between bg-transparent px-5 py-4 backdrop-blur-md  border-stroke-neutral-decorative border">
        <div className="max-w-[20%]">
          <Link href="/" passHref className="inline list-none px-2.5">
            <Image
              src="/recon-logo.svg"
              alt="Recon Logo"
              width={70}
              height={60}
              priority
            />
          </Link>
        </div>
        <div className="hidden max-w-[85%] flex-row items-center justify-between lg:flex">
          <div className="flex flex-row justify-between px-5">
            {navLinks.map(({ href, label, external }) => (
              <Link
                key={label}
                href={href}
                passHref
                className="inline list-none px-2.5"
                target={external ? "_blank" : undefined}
                rel={external ? "noopener noreferrer" : undefined}
              >
                <Body3 color="secondary">{label}</Body3>
              </Link>
            ))}
          </div>
          <div className="flex gap-4 px-5">
            {telegramBtn}
            {loginBtn}
          </div>
        </div>
        <div className="flex items-center lg:hidden">
          <button
            onClick={toggleMenu}
            className="text-fore-on-accent-primary focus:outline-none"
          >
            {isMenuOpen ? (
              <LiaTimesSolid className="size-8" />
            ) : (
              <HiOutlineBars3BottomLeft className="size-8" />
            )}
          </button>
        </div>
      </div>

      {isMenuOpen && (
        <div className="fixed inset-0 top-20 z-[999] flex flex-col items-center justify-center bg-back-neutral-tertiary/95 backdrop-blur-xl">
          <ul className="flex w-full flex-col items-center space-y-8">
            {navLinks.map(({ href, label, external }) => (
              <Link
                key={label}
                href={href}
                passHref
                className="uppercase"
                onClick={closeMenu}
                target={external ? "_blank" : undefined}
                rel={external ? "noopener noreferrer" : undefined}
              >
                <Body3 color="secondary">{label}</Body3>
              </Link>
            ))}
          </ul>
          <div className="mt-8 flex w-full flex-col items-center space-y-4">
            {telegramBtn}
          </div>
        </div>
      )}
    </div>
  );
};

export default PromoNavbar;
