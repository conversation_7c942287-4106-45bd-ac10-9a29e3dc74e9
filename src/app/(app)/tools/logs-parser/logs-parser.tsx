import type { Fuzzer, FuzzingResults } from "@recon-fuzz/log-parser";
import {
  echidnaLogsToFunctions,
  halmosSequenceToFunction,
  medusaLogsToFunctions,
  processLogs,
} from "@recon-fuzz/log-parser";
import { useEffect, useState } from "react";
import { IoMdDownload } from "react-icons/io";
import { toast } from "sonner";

import { ENV_TYPE } from "@/app/app.constants";
import type { BrokenPropShow, TracesShower } from "@/app/types/types";
import type { GlobalVmConfig } from "@/app/utils/vm-config";
import { getCurrentVmConfig, updateVmConfig } from "@/app/utils/vm-config";

import { AppButton } from "../../../components/app-button";
import { AppCode } from "../../../components/app-code";
import { H5, Title3Strong } from "../../../components/app-typography";

interface LogComponentProps {
  fuzzer: ENV_TYPE;
  logs: string;
  prefix?: string;
  jobStatsForced?: FuzzingResults;
}

interface VmButtonsProps {
  globalVmConfig: GlobalVmConfig;
  onVmConfigChange: (config: GlobalVmConfig) => void;
}

const VmButtons = ({ globalVmConfig, onVmConfigChange }: VmButtonsProps) => {
  const handleToggle = (property: keyof GlobalVmConfig) => {
    const newConfig = {
      ...globalVmConfig,
      [property]: !globalVmConfig[property],
    };
    onVmConfigChange(newConfig);
  };

  return (
    <div className="flex gap-2 max-[900px]:hidden">
      <AppButton
        variant={globalVmConfig.prank ? "primary" : "outline"}
        size="sm"
        onClick={() => handleToggle("prank")}
      >
        <span>Use vm.prank</span>
      </AppButton>
      <AppButton
        variant={globalVmConfig.roll ? "primary" : "outline"}
        size="sm"
        onClick={() => handleToggle("roll")}
      >
        <span>Use vm.roll</span>
      </AppButton>
      <AppButton
        variant={globalVmConfig.time ? "primary" : "outline"}
        size="sm"
        onClick={() => handleToggle("time")}
      >
        <span>Use vm.warp</span>
      </AppButton>
    </div>
  );
};

interface BrokenPropertyHeaderProps {
  index: number;
  brokenPropName: string;
  isExpanded: boolean;
  onToggle: () => void;
}

const BrokenPropertyHeader = ({
  index,
  brokenPropName,
  isExpanded,
  onToggle,
}: BrokenPropertyHeaderProps) => (
  <div
    onClick={onToggle}
    className="cursor-pointer p-3 text-fore-neutral-primary hover:text-fore-neutral-secondary transition-colors"
  >
    <Title3Strong>{`${index + 1} - ${brokenPropName} ${
      isExpanded ? "▲" : "▼"
    }`}</Title3Strong>
  </div>
);

export default function LogComponent({
  fuzzer,
  logs,
  prefix,
  jobStatsForced,
}: LogComponentProps) {
  const [showTrace, setShowTrace] = useState<TracesShower[]>(null);
  const [globalVmConfig, setGlobalVmConfig] = useState<GlobalVmConfig>({
    prank: false,
    roll: false,
    time: false,
  });
  const [traces, setTraces] = useState<string[]>([]);
  const [jobStatsGlobal, setJobStatsGlobal] = useState<FuzzingResults>();
  const [showBrokenProp, setShowBrokenProp] = useState<BrokenPropShow[]>(null);

  useEffect(() => {
    const vmConfig = getCurrentVmConfig(fuzzer);
    setGlobalVmConfig(vmConfig);

    let jobStats: FuzzingResults = {
      duration: "0",
      coverage: 0,
      failed: 0,
      passed: 0,
      numberOfTests: 0,
      results: [],
      traces: [],
      brokenProperties: [],
    };
    if (jobStatsForced && jobStatsForced.brokenProperties.length > 0) {
      jobStats = jobStatsForced;
      setJobStatsGlobal(jobStats);
    } else if (logs && fuzzer) {
      const fz = (typeof fuzzer === "string"
        ? (fuzzer as string).toUpperCase()
        : (fuzzer as unknown as string)) as unknown as Fuzzer;
      jobStats = processLogs(logs, fz); /// TODO 0xsi please fix -Alex
      setJobStatsGlobal(jobStats);
    }
    if (logs && fuzzer && jobStats.brokenProperties.length > 0) {
      setTraces(jobStats.brokenProperties.map((el) => el.sequence));
      jobStats.brokenProperties.forEach((trace, index) => {
        setShowTrace((prev) => [
          ...(prev || []),
          {
            id: index,
            show: false,
          },
        ]);
        setShowBrokenProp((prev) => [
          ...(prev || []),
          {
            id: index,
            show: false,
          },
        ]);
      });
    }
  }, [logs, fuzzer, jobStatsForced]);

  const showTracesHandler = (index: number) => {
    setShowTrace((prev) =>
      prev.map((trace) => {
        if (trace.id === index) {
          return {
            ...trace,
            show: !trace.show,
          };
        }
        return trace;
      })
    );
  };

  const showBrokenPropHandler = (index: number) => {
    setShowBrokenProp((prev) =>
      prev.map((trace) => {
        if (trace.id === index) {
          return {
            ...trace,
            show: !trace.show,
          };
        }
        return trace;
      })
    );
  };

  const handleVmConfigChange = (newConfig: GlobalVmConfig) => {
    setGlobalVmConfig(newConfig);
    updateVmConfig(globalVmConfig, newConfig);
  };

  const prepareTrace = (
    trace: string,
    index: number,
    brokenProperties: string
  ) => {
    let finalTrace = "";
    if (fuzzer === ENV_TYPE.MEDUSA) {
      finalTrace = medusaLogsToFunctions(
        trace,
        prefix ?? index.toString(),
        globalVmConfig
      );
    } else if (fuzzer === ENV_TYPE.ECHIDNA) {
      finalTrace = echidnaLogsToFunctions(
        trace,
        prefix ?? index.toString(),
        brokenProperties,
        globalVmConfig
      );
    } else if (fuzzer === ENV_TYPE.HALMOS) {
      finalTrace = halmosSequenceToFunction(
        trace,
        brokenProperties,
        prefix ?? index.toString(),
        index
      );
    }
    const functionName = finalTrace
      .split("() public")[0]
      .replace("function ", "");
    const forgeCommand =
      `// forge test --match-test ${functionName} -vvv`.replace("\n", "");
    return `${forgeCommand} \n${finalTrace}`;
  };

  const getBrokenPropName = (index: number) => {
    const brkProp = jobStatsGlobal.brokenProperties.find(
      (_, id) => index === id
    )?.brokenProperty;
    return brkProp;
  };

  const copyAllHandler = async () => {
    try {
      const allFn = traces.map((trace, index) => {
        return prepareTrace(
          trace,
          index,
          jobStatsGlobal.brokenProperties.find((el) => el.sequence === trace)
            ?.brokenProperty
        );
      });

      await navigator.clipboard.writeText(allFn.join("\n\n"));
      toast.success("All reproduction code copied to clipboard!");
    } catch (error) {
      console.error("Failed to copy:", error);
      toast.error("Failed to copy to clipboard");
    }
  };

  return (
    <div>
      {traces.length > 0 ? (
        <div className="flex flex-row items-center justify-between gap-4">
          <H5 className="my-5">
            {traces.length} Broken propert{traces.length === 1 ? "y" : "ies"}
          </H5>
          <AppButton
            className=" px-10 py-2.5"
            onClick={copyAllHandler}
            rightIcon={<IoMdDownload />}
          >
            Copy all repro
          </AppButton>
        </div>
      ) : null}
      {traces.length > 0
        ? traces.map((trace, index) => {
            const brokenPropName = getBrokenPropName(index);
            const isExpanded =
              showBrokenProp.find((el) => el.id === index)?.show || false;
            const showTraceForIndex =
              showTrace.find((el) => el.id === index)?.show || false;

            return (
              <div key={`trace-${index}-${brokenPropName}`}>
                <BrokenPropertyHeader
                  index={index}
                  brokenPropName={brokenPropName}
                  isExpanded={isExpanded}
                  onToggle={() => showBrokenPropHandler(index)}
                />
                {isExpanded && (
                  <div>
                    <div className="mb-4 flex flex-wrap items-center justify-between gap-2 rounded-lg bg-back-neutral-secondary p-3">
                      <div className="flex flex-wrap gap-2">
                        <VmButtons
                          globalVmConfig={globalVmConfig}
                          onVmConfigChange={handleVmConfigChange}
                        />
                      </div>
                      <div className="flex gap-2">
                        <AppButton
                          variant="primary"
                          size="sm"
                          onClick={() => showTracesHandler(index)}
                        >
                          <span>
                            {showTraceForIndex ? "Hide trace" : "Show trace"}
                          </span>
                        </AppButton>
                      </div>
                    </div>
                    <div>
                      <AppCode
                        key={`code-${brokenPropName}-${index}`}
                        code={prepareTrace(
                          trace,
                          index,
                          jobStatsGlobal.brokenProperties.find(
                            (el) => el.sequence === trace
                          )?.brokenProperty || ""
                        )}
                        language="javascript"
                      />
                    </div>
                    {showTraceForIndex && (
                      <div className="mt-4">
                        <AppCode code={traces[index]} language="bash" />
                      </div>
                    )}
                  </div>
                )}
              </div>
            );
          })
        : null}
    </div>
  );
}
