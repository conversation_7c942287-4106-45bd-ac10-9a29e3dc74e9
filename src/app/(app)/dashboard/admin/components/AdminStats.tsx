"use client";
import React from "react";
import { Title2Strong, Body2, Body3 } from "@/app/components/app-typography";
import type { Organization } from "@/app/services/organization.hooks";

interface AdminStatsProps {
  allOrgs: Organization[];
  runningJobCount: number;
}

interface StatCardProps {
  title: string;
  count: number;
  icon: string;
  color: "success" | "warning" | "error" | "info";
  organizations?: Organization[];
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  count,
  icon,
  color,
  organizations = [],
}) => {
  const getColorClasses = (color: string) => {
    switch (color) {
      case "success":
        return {
          bg: "bg-status-success/10",
          border: "border-status-success/20",
          text: "text-status-success",
        };
      case "warning":
        return {
          bg: "bg-accent-primary/10",
          border: "border-accent-primary/20",
          text: "text-accent-primary",
        };
      case "error":
        return {
          bg: "bg-status-error/10",
          border: "border-status-error/20",
          text: "text-status-error",
        };
      case "info":
        return {
          bg: "bg-back-neutral-secondary",
          border: "border-stroke-neutral-decorative",
          text: "text-fore-neutral-primary",
        };
      default:
        return {
          bg: "bg-back-neutral-secondary",
          border: "border-stroke-neutral-decorative",
          text: "text-fore-neutral-primary",
        };
    }
  };

  const colorClasses = getColorClasses(color);

  return (
    <div
      className={`${colorClasses.bg} ${colorClasses.border} border rounded-xl p-6 hover:shadow-lg transition-all duration-300`}
    >
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-3">
          <span className="text-2xl">{icon}</span>
          <Title2Strong color="primary">{title}</Title2Strong>
        </div>
        <div className={`text-3xl font-bold ${colorClasses.text}`}>
          {count}
        </div>
      </div>
      
      {organizations.length > 0 && (
        <div className="space-y-2 max-h-32 overflow-y-auto">
          {organizations.slice(0, 3).map((org) => (
            <div
              key={org.id}
              className="flex items-center justify-between p-2 bg-back-neutral-primary rounded-lg"
            >
              <Body3 color="primary" className="truncate">
                {org.name}
              </Body3>
              <Body3 color="secondary" className="text-xs">
                {org.id.slice(0, 8)}...
              </Body3>
            </div>
          ))}
          {organizations.length > 3 && (
            <Body3 color="tertiary" className="text-center text-xs">
              +{organizations.length - 3} more
            </Body3>
          )}
        </div>
      )}
    </div>
  );
};

export const AdminStats: React.FC<AdminStatsProps> = ({
  allOrgs,
  runningJobCount,
}) => {
  const paidOrgs = allOrgs.filter((org) => org.billingStatus === "PAID");
  const trialOrgs = allOrgs.filter((org) => org.billingStatus === "TRIAL");
  const unpaidOrgs = allOrgs.filter((org) => org.billingStatus === "UNPAID");

  return (
    <div className="mb-8">
      <div className="mb-6">
        <Title2Strong color="primary" className="mb-2">
          System Overview
        </Title2Strong>
        <Body2 color="secondary">
          Current system statistics and organization breakdown
        </Body2>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        <StatCard
          title="Paid Orgs"
          count={paidOrgs.length}
          icon="💰"
          color="success"
          organizations={paidOrgs}
        />
        <StatCard
          title="Trial Orgs"
          count={trialOrgs.length}
          icon="🔄"
          color="warning"
          organizations={trialOrgs}
        />
        <StatCard
          title="Unpaid Orgs"
          count={unpaidOrgs.length}
          icon="⏳"
          color="error"
          organizations={unpaidOrgs}
        />
        <StatCard
          title="Running Jobs"
          count={runningJobCount}
          icon="🚀"
          color="info"
        />
      </div>
    </div>
  );
};
