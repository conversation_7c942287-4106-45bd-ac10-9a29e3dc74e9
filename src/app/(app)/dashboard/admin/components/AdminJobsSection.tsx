"use client";
import React from "react";
import { AppButton } from "@/app/components/app-button";
import { AppCode } from "@/app/components/app-code";
import { Title2Strong, Body2, Body3 } from "@/app/components/app-typography";
import type { Job } from "@/app/services/jobs.hooks";

interface AdminJobsSectionProps {
  queuedJobs: Job[];
  displayQueuedJobs: boolean;
  onToggleQueuedJobs: () => void;
  onRefreshQueuedJobs: () => void;
}

interface JobCardProps {
  job: Job;
}

const JobCard: React.FC<JobCardProps> = ({ job }) => {
  const getStatusColor = (status: string) => {
    switch (status) {
      case "QUEUED":
        return "text-accent-primary bg-accent-primary/10 border-accent-primary/20";
      case "RUNNING":
        return "text-status-success bg-status-success/10 border-status-success/20";
      case "SUCCESS":
        return "text-status-success bg-status-success/10 border-status-success/20";
      case "ERROR":
        return "text-status-error bg-status-error/10 border-status-error/20";
      case "STOPPED":
        return "text-fore-neutral-secondary bg-back-neutral-secondary border-stroke-neutral-decorative";
      default:
        return "text-fore-neutral-secondary bg-back-neutral-secondary border-stroke-neutral-decorative";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "QUEUED":
        return "⏳";
      case "RUNNING":
        return "🔄";
      case "SUCCESS":
        return "✅";
      case "ERROR":
        return "❌";
      case "STOPPED":
        return "⏹️";
      default:
        return "❓";
    }
  };

  return (
    <div className="bg-back-neutral-primary rounded-lg border border-stroke-neutral-decorative p-4 hover:shadow-md transition-all duration-200">
      <div className="flex items-start justify-between mb-3">
        <div className="flex-1 min-w-0">
          <div className="flex items-center gap-2 mb-2">
            <Body3 color="primary" className="font-medium truncate">
              {job.label || `Job ${job.id.slice(0, 8)}...`}
            </Body3>
            <div
              className={`px-2 py-1 rounded-full border text-xs font-medium ${getStatusColor(
                job.status
              )}`}
            >
              <span className="mr-1">{getStatusIcon(job.status)}</span>
              {job.status}
            </div>
          </div>
          
          <div className="space-y-1">
            <Body3 color="secondary" className="text-xs">
              ID: {job.id}
            </Body3>
            {job.organizationId && (
              <Body3 color="secondary" className="text-xs">
                Org: {job.organizationId.slice(0, 12)}...
              </Body3>
            )}
            {job.createdAt && (
              <Body3 color="secondary" className="text-xs">
                Created: {new Date(job.createdAt).toLocaleString()}
              </Body3>
            )}
          </div>
        </div>
      </div>

      {/* Job Details */}
      <div className="mt-3 pt-3 border-t border-stroke-neutral-decorative">
        <details className="group">
          <summary className="cursor-pointer list-none">
            <div className="flex items-center justify-between">
              <Body3 color="secondary" className="text-xs">
                View Details
              </Body3>
              <span className="text-xs transition-transform duration-200 group-open:rotate-180">
                ▼
              </span>
            </div>
          </summary>
          <div className="mt-3">
            <AppCode
              language="json"
              code={JSON.stringify(job, null, 2)}
            />
          </div>
        </details>
      </div>
    </div>
  );
};

export const AdminJobsSection: React.FC<AdminJobsSectionProps> = ({
  queuedJobs,
  displayQueuedJobs,
  onToggleQueuedJobs,
  onRefreshQueuedJobs,
}) => {
  return (
    <div className="mb-8">
      <div className="bg-back-neutral-tertiary rounded-xl border border-stroke-neutral-decorative overflow-hidden">
        {/* Header */}
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-3">
              <span className="text-2xl">🚀</span>
              <Title2Strong color="primary">
                Queued Jobs ({queuedJobs.length})
              </Title2Strong>
            </div>
            <div className="flex items-center gap-3">
              <AppButton
                variant="outline"
                size="sm"
                onClick={onRefreshQueuedJobs}
                leftIcon={<span>🔄</span>}
              >
                Refresh
              </AppButton>
              <AppButton
                variant="secondary"
                size="sm"
                onClick={onToggleQueuedJobs}
              >
                {displayQueuedJobs ? "Hide Jobs" : "Show Jobs"}
              </AppButton>
            </div>
          </div>
          
          <Body2 color="secondary">
            Monitor and manage jobs currently in the system queue
          </Body2>
        </div>

        {/* Jobs List */}
        {displayQueuedJobs && (
          <div className="p-6 pt-0 border-t border-stroke-neutral-decorative">
            {queuedJobs.length === 0 ? (
              <div className="text-center py-12">
                <span className="text-4xl mb-4 block">📭</span>
                <Body2 color="secondary">No queued jobs found</Body2>
                <Body3 color="tertiary" className="mt-2">
                  All jobs are either completed or not yet submitted
                </Body3>
              </div>
            ) : (
              <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-4">
                {queuedJobs.map((job) => (
                  <JobCard key={job.id} job={job} />
                ))}
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
};
