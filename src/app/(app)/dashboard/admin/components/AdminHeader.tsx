"use client";
import React from "react";
import { Heading, Body2 } from "@/app/components/app-typography";

interface AdminHeaderProps {
  isLoading: boolean;
  isSuperAdmin: boolean;
}

export const AdminHeader: React.FC<AdminHeaderProps> = ({
  isLoading,
  isSuperAdmin,
}) => {
  if (isLoading) {
    return (
      <div className="mb-8">
        <div className="flex items-center justify-center p-8">
          <div className="animate-pulse flex items-center gap-3">
            <div className="w-6 h-6 bg-accent-primary/20 rounded-full animate-spin border-2 border-accent-primary border-t-transparent"></div>
            <Body2 color="secondary">Loading admin panel...</Body2>
          </div>
        </div>
      </div>
    );
  }

  if (!isSuperAdmin) {
    return (
      <div className="mb-8">
        <div className="bg-status-error/10 border border-status-error/20 rounded-xl p-6 text-center">
          <div className="mb-2">
            <span className="text-2xl">🚫</span>
          </div>
          <Heading color="primary" className="mb-2">
            Access Denied
          </Heading>
          <Body2 color="secondary">
            You don't have permission to access the admin panel.
          </Body2>
        </div>
      </div>
    );
  }

  return (
    <div className="mb-8">
      <div className="bg-back-neutral-tertiary rounded-xl border border-stroke-neutral-decorative p-6">
        <div className="flex items-center gap-3 mb-2">
          <span className="text-2xl">⚡</span>
          <Heading color="primary">Admin Dashboard</Heading>
        </div>
        <Body2 color="secondary">
          Manage organizations, users, and system operations
        </Body2>
      </div>
    </div>
  );
};
