"use client";
import React from "react";
import { AppButton } from "@/app/components/app-button";
import { AppInput } from "@/app/components/app-input";
import { AppSelect } from "@/app/components/app-select";
import { Title2Strong, Body2, Body3 } from "@/app/components/app-typography";
import type { Organization } from "@/app/services/organization.hooks";

interface User {
  id: string;
  organizationId: string;
  username?: string;
}

interface AdminActionsProps {
  selectedOrg: Organization | null;
  selectedUser: User | null;
  selectedStatus: string | null;
  jobId: string | null;
  orgInvite: string | null;
  billingStatus: string[];
  users: User[];
  onStatusChange: (value: string) => void;
  onOrgStatusUpdate: () => void;
  onCreateOrgInvite: () => void;
  onSwitchOrg: () => void;
  onJobIdChange: (value: string) => void;
  onDeleteJob: () => void;
  onDeleteOrg: () => void;
}

interface ActionCardProps {
  title: string;
  icon: string;
  children: React.ReactNode;
  variant?: "default" | "warning" | "danger";
}

const ActionCard: React.FC<ActionCardProps> = ({
  title,
  icon,
  children,
  variant = "default",
}) => {
  const getVariantClasses = () => {
    switch (variant) {
      case "warning":
        return "bg-accent-primary/5 border-accent-primary/20";
      case "danger":
        return "bg-status-error/5 border-status-error/20";
      default:
        return "bg-back-neutral-tertiary border-stroke-neutral-decorative";
    }
  };

  return (
    <div
      className={`${getVariantClasses()} border rounded-xl p-6 hover:shadow-lg transition-all duration-300`}
    >
      <div className="flex items-center gap-3 mb-4">
        <span className="text-xl">{icon}</span>
        <Title2Strong color="primary" className="text-lg">
          {title}
        </Title2Strong>
      </div>
      {children}
    </div>
  );
};

export const AdminActions: React.FC<AdminActionsProps> = ({
  selectedOrg,
  selectedUser,
  selectedStatus,
  jobId,
  orgInvite,
  billingStatus,
  users,
  onStatusChange,
  onOrgStatusUpdate,
  onCreateOrgInvite,
  onSwitchOrg,
  onJobIdChange,
  onDeleteJob,
  onDeleteOrg,
}) => {
  return (
    <div className="mb-8">
      <div className="mb-6">
        <Title2Strong color="primary" className="mb-2">
          Administrative Actions
        </Title2Strong>
        <Body2 color="secondary">
          Perform various administrative operations on organizations and users
        </Body2>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 xl:grid-cols-3 gap-6">
        {/* Users in Organization */}
        <ActionCard title="Organization Users" icon="👥">
          {selectedOrg ? (
            selectedOrg.users && selectedOrg.users.length > 0 ? (
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {selectedOrg.users.map((user) => (
                  <div
                    key={user.id}
                    className="flex items-center justify-between p-3 bg-back-neutral-secondary rounded-lg"
                  >
                    <div>
                      <Body3 color="primary" className="font-medium">
                        {users.find((u) => u.id === user.id)?.username || "Unknown"}
                      </Body3>
                      <Body3 color="secondary" className="text-xs">
                        {user.id.slice(0, 12)}...
                      </Body3>
                    </div>
                  </div>
                ))}
              </div>
            ) : (
              <Body3 color="tertiary">No users in this organization</Body3>
            )
          ) : (
            <Body3 color="tertiary">Select an organization to view users</Body3>
          )}
        </ActionCard>

        {/* Create Organization Invite */}
        <ActionCard title="Organization Invite" icon="📧">
          {selectedOrg ? (
            <div className="space-y-4">
              <AppButton
                variant="secondary"
                size="sm"
                onClick={onCreateOrgInvite}
                fullWidth
              >
                Generate Invite Code
              </AppButton>
              {orgInvite && (
                <div className="p-3 bg-accent-primary/10 border border-accent-primary/20 rounded-lg">
                  <Body3 color="primary" className="font-medium mb-1">
                    Invite Code:
                  </Body3>
                  <Body3 color="secondary" className="font-mono text-xs break-all">
                    {orgInvite}
                  </Body3>
                </div>
              )}
            </div>
          ) : (
            <Body3 color="tertiary">Select an organization to create invite</Body3>
          )}
        </ActionCard>

        {/* Switch Organization Status */}
        <ActionCard title="Organization Status" icon="🔄" variant="warning">
          {selectedOrg ? (
            <div className="space-y-4">
              <div className="p-3 bg-back-neutral-secondary rounded-lg">
                <Body3 color="secondary" className="text-xs mb-1">
                  Current Status:
                </Body3>
                <Body3 color="primary" className="font-medium">
                  {selectedOrg.billingStatus}
                </Body3>
              </div>
              <AppSelect
                options={billingStatus
                  .filter((status) => status !== selectedOrg.billingStatus)
                  .map((status) => ({ value: status, label: status }))}
                placeholder="Select new status"
                onChange={(e) => onStatusChange(e.target.value)}
                value={selectedStatus || ""}
              />
              {selectedStatus && (
                <AppButton
                  variant="primary"
                  size="sm"
                  onClick={onOrgStatusUpdate}
                  fullWidth
                >
                  Update Status
                </AppButton>
              )}
            </div>
          ) : (
            <Body3 color="tertiary">Select an organization to change status</Body3>
          )}
        </ActionCard>

        {/* Switch User Organization */}
        <ActionCard title="Move User to Org" icon="🔀">
          {selectedOrg && selectedUser ? (
            <div className="space-y-4">
              <div className="p-3 bg-back-neutral-secondary rounded-lg">
                <Body3 color="secondary" className="text-xs mb-1">
                  Moving:
                </Body3>
                <Body3 color="primary" className="font-medium">
                  {selectedUser.username || selectedUser.id.slice(0, 8)}...
                </Body3>
                <Body3 color="secondary" className="text-xs mt-1">
                  To: {selectedOrg.name}
                </Body3>
              </div>
              <AppButton
                variant="secondary"
                size="sm"
                onClick={onSwitchOrg}
                fullWidth
              >
                Move User
              </AppButton>
            </div>
          ) : (
            <Body3 color="tertiary">
              Select both an organization and user to move
            </Body3>
          )}
        </ActionCard>

        {/* Delete Job */}
        <ActionCard title="Delete Job" icon="🗑️" variant="danger">
          <div className="space-y-4">
            <AppInput
              type="text"
              placeholder="Enter job ID"
              value={jobId || ""}
              onChange={(e) => onJobIdChange(e.target.value)}
            />
            <AppButton
              variant="outline"
              size="sm"
              onClick={onDeleteJob}
              disabled={!jobId}
              fullWidth
              className="text-status-error border-status-error hover:bg-status-error/10"
            >
              Delete Job
            </AppButton>
          </div>
        </ActionCard>

        {/* Delete Organization */}
        <ActionCard title="Delete Organization" icon="⚠️" variant="danger">
          {selectedOrg ? (
            <div className="space-y-4">
              <div className="p-3 bg-status-error/10 border border-status-error/20 rounded-lg">
                <Body3 color="primary" className="font-medium mb-1">
                  ⚠️ Danger Zone
                </Body3>
                <Body3 color="secondary" className="text-xs">
                  This action cannot be undone
                </Body3>
              </div>
              <AppButton
                variant="outline"
                size="sm"
                onClick={onDeleteOrg}
                fullWidth
                className="text-status-error border-status-error hover:bg-status-error/10"
              >
                Delete Organization
              </AppButton>
            </div>
          ) : (
            <Body3 color="tertiary">Select an organization to delete</Body3>
          )}
        </ActionCard>
      </div>
    </div>
  );
};
