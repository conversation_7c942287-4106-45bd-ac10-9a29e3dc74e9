"use client";
import React from "react";
import { AppButton } from "@/app/components/app-button";
import { Title2Strong, Body2, Body3 } from "@/app/components/app-typography";
import { SearchDropdown } from "@/app/(app)/components/search-dropdown";
import type { Organization } from "@/app/services/organization.hooks";

interface User {
  id: string;
  organizationId: string;
  username?: string;
}

interface AdminSelectorsProps {
  allOrgs: Organization[];
  users: User[];
  selectedOrg: Organization | null;
  selectedUser: User | null;
  onOrgSelect: (value: string) => void;
  onUserSelect: (value: string) => void;
  onClearCache: () => void;
}

export const AdminSelectors: React.FC<AdminSelectorsProps> = ({
  allOrgs,
  users,
  selectedOrg,
  selectedUser,
  onOrgSelect,
  onUserSelect,
  onClearCache,
}) => {
  return (
    <div className="mb-8">
      <div className="bg-back-neutral-tertiary rounded-xl border border-stroke-neutral-decorative p-6">
        <div className="mb-6">
          <Title2Strong color="primary" className="mb-2">
            Data Selection
          </Title2Strong>
          <Body2 color="secondary">
            Select organizations and users to perform administrative actions
          </Body2>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6 mb-6">
          {/* Organization Selector */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <span className="text-lg">🏢</span>
              <Body3 color="primary" className="font-medium">
                Organization Selection
              </Body3>
            </div>
            {allOrgs.length > 0 ? (
              <SearchDropdown
                items={allOrgs.map((org) => ({
                  label: `${org.name} (${org.billingStatus}) - ${org.id.slice(0, 8)}...`,
                  value: org.id,
                }))}
                onChange={onOrgSelect}
                searchPlaceholder="Search organizations..."
              />
            ) : (
              <div className="p-4 bg-back-neutral-secondary rounded-lg border border-stroke-neutral-decorative">
                <Body3 color="tertiary">No organizations available</Body3>
              </div>
            )}
            
            {selectedOrg && (
              <div className="p-4 bg-accent-primary/10 border border-accent-primary/20 rounded-lg">
                <Body3 color="primary" className="font-medium mb-1">
                  Selected: {selectedOrg.name}
                </Body3>
                <Body3 color="secondary" className="text-xs">
                  Status: {selectedOrg.billingStatus} • ID: {selectedOrg.id}
                </Body3>
                <Body3 color="secondary" className="text-xs">
                  Users: {selectedOrg.users?.length || 0}
                </Body3>
              </div>
            )}
          </div>

          {/* User Selector */}
          <div className="space-y-3">
            <div className="flex items-center gap-2">
              <span className="text-lg">👤</span>
              <Body3 color="primary" className="font-medium">
                User Selection
              </Body3>
            </div>
            {users.length > 0 ? (
              <SearchDropdown
                items={users.map((user) => ({
                  label: `${user.username || "Unknown"} - ${user.id.slice(0, 8)}...`,
                  value: user.id,
                }))}
                onChange={onUserSelect}
                searchPlaceholder="Search users..."
              />
            ) : (
              <div className="p-4 bg-back-neutral-secondary rounded-lg border border-stroke-neutral-decorative">
                <Body3 color="tertiary">No users available</Body3>
              </div>
            )}
            
            {selectedUser && (
              <div className="p-4 bg-accent-primary/10 border border-accent-primary/20 rounded-lg">
                <Body3 color="primary" className="font-medium mb-1">
                  Selected: {selectedUser.username || "Unknown User"}
                </Body3>
                <Body3 color="secondary" className="text-xs">
                  ID: {selectedUser.id}
                </Body3>
                <Body3 color="secondary" className="text-xs">
                  Org ID: {selectedUser.organizationId}
                </Body3>
              </div>
            )}
          </div>
        </div>

        {/* Cache Management */}
        <div className="pt-4 border-t border-stroke-neutral-decorative">
          <div className="flex items-center justify-between">
            <div>
              <Body3 color="primary" className="font-medium mb-1">
                Cache Management
              </Body3>
              <Body3 color="secondary" className="text-xs">
                Clear cached data and refresh all information
              </Body3>
            </div>
            <AppButton
              variant="outline"
              size="sm"
              onClick={onClearCache}
              leftIcon={<span>🔄</span>}
            >
              Clear Cache & Reset
            </AppButton>
          </div>
        </div>
      </div>
    </div>
  );
};
