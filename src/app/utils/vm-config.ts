import { ENV_TYPE } from "@/app/app.constants";

export interface GlobalVmConfig {
  prank: boolean;
  roll: boolean;
  time: boolean;
}

export interface VmConfigWithDefaults extends GlobalVmConfig {
  isUsingDefaults: boolean;
  currentTool: ENV_TYPE | null;
}

const VM_CONFIG_STORAGE_KEY = "recon-global-vm-config";

const TOOL_DEFAULTS: Record<ENV_TYPE, GlobalVmConfig> = {
  [ENV_TYPE.ECHIDNA]: {
    prank: true,
    roll: true,
    time: true,
  },
  [ENV_TYPE.MEDUSA]: {
    prank: false,
    roll: false,
    time: false,
  },
  [ENV_TYPE.HALMOS]: {
    prank: false,
    roll: false,
    time: false,
  },
  [ENV_TYPE.FOUNDRY]: {
    prank: false,
    roll: false,
    time: false,
  },
  [ENV_TYPE.KONTROL]: {
    prank: false,
    roll: false,
    time: false,
  },
};

export function getToolDefaults(tool: ENV_TYPE): GlobalVmConfig {
  return TOOL_DEFAULTS[tool];
}

export function loadVmConfigFromStorage(): GlobalVmConfig | null {
  if (typeof window === "undefined") return null;

  try {
    const stored = localStorage.getItem(VM_CONFIG_STORAGE_KEY);
    if (!stored) return null;

    const parsed = JSON.parse(stored);

    // Validate the structure
    if (
      typeof parsed === "object" &&
      typeof parsed.prank === "boolean" &&
      typeof parsed.roll === "boolean" &&
      typeof parsed.time === "boolean"
    ) {
      return parsed;
    }

    return null;
  } catch (error) {
    console.warn("Failed to load VM config from localStorage:", error);
    return null;
  }
}

export function saveVmConfigToStorage(config: GlobalVmConfig): void {
  if (typeof window === "undefined") return;

  try {
    localStorage.setItem(VM_CONFIG_STORAGE_KEY, JSON.stringify(config));
    // Dispatch custom event for same-tab synchronization
    window.dispatchEvent(
      new CustomEvent("vm-config-changed", { detail: config })
    );
  } catch (error) {
    console.warn("Failed to save VM config to localStorage:", error);
  }
}

export function getCurrentVmConfig(tool: ENV_TYPE): VmConfigWithDefaults {
  const storedConfig = loadVmConfigFromStorage();

  if (storedConfig) {
    return {
      ...storedConfig,
      isUsingDefaults: false,
      currentTool: tool,
    };
  }

  const defaults = getToolDefaults(tool);
  return {
    ...defaults,
    isUsingDefaults: true,
    currentTool: tool,
  };
}

export function resetToToolDefaults(tool: ENV_TYPE): GlobalVmConfig {
  const defaults = getToolDefaults(tool);
  saveVmConfigToStorage(defaults);
  return defaults;
}

export function updateVmConfig(
  currentConfig: GlobalVmConfig,
  updates: Partial<GlobalVmConfig>
): GlobalVmConfig {
  const newConfig = {
    ...currentConfig,
    ...updates,
  };

  saveVmConfigToStorage(newConfig);
  return newConfig;
}

export function isUsingToolDefaults(
  config: GlobalVmConfig,
  tool: ENV_TYPE
): boolean {
  const defaults = getToolDefaults(tool);
  return (
    config.prank === defaults.prank &&
    config.roll === defaults.roll &&
    config.time === defaults.time
  );
}
