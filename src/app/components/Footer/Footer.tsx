import { useGetDiscord } from "@/app/services/discord.hook.ts";
import Image from "next/image";
import Link from "next/link";
import React from "react";
import { ReconLogoIcon } from "../recon-logo-icon";
import { Title2Strong, Title3Strong, Attribution } from "../app-typography";

interface FooterLinkProps {
  href: string;
  children: React.ReactNode;
  className?: string;
}

const FooterLink = ({ href, children, className = "" }: FooterLinkProps) => (
  <Link
    href={href}
    className={`block transition-all duration-200 hover:underline hover:text-accent-primary ${className}`}
  >
    <Title3Strong color="primary">{children}</Title3Strong>
  </Link>
);

interface FooterSectionProps {
  title: string;
  children: React.ReactNode;
}

const FooterSection = ({ title, children }: FooterSectionProps) => (
  <div className="flex flex-col">
    <Title2Strong color="primary" className="mb-4 uppercase">
      {title}
    </Title2Strong>
    <div className="space-y-3">{children}</div>
  </div>
);

export default function Footer() {
  const { data: discordUrl } = useGetDiscord();

  return (
    <div className="flex w-full flex-col bg-back-neutral-tertiary relative z-10">
      <div className="flex size-full flex-col justify-center gap-8 p-6 md:grid-cols-2 lg:grid lg:grid-cols-4 lg:p-10">
        <FooterSection title="Connect with us">
          <FooterLink href="https://x.com/getreconxyz">X || Twitter</FooterLink>
          <FooterLink href="https://github.com/Recon-Fuzz">Github</FooterLink>
          {discordUrl && <FooterLink href={discordUrl}>Discord</FooterLink>}
          <FooterLink href="https://getrecon.substack.com/">
            Substack
          </FooterLink>
        </FooterSection>
        <FooterSection title="Developers">
          <FooterLink href="/dashboard">Login</FooterLink>
          <FooterLink href="/dynamic-replacement-demo">
            Dynamic Replacement Demo
          </FooterLink>
          <FooterLink href="https://t.me/gallodasballo">
            Talk to Sales
          </FooterLink>
          <FooterLink href="https://docs.google.com/presentation/d/1XX48swlia6O_Paqsvv3Tt466d1Lgh0zggSOqy8TPZcM/edit?usp=sharing">
            Live Exploit Prevention (Stateful Monitoring)
          </FooterLink>
          <FooterLink href="https://book.getrecon.xyz/">Docs</FooterLink>
        </FooterSection>
        <FooterSection title="Tools">
          <FooterLink href="/tools/">
            All Tools
          </FooterLink>
          <FooterLink href="/tools/medusa">Medusa Logs Scraper</FooterLink>
          <FooterLink href="/tools/echidna">Echidna Logs Scraper</FooterLink>
          <FooterLink href="/tools/sandbox">Invariant Test Builder</FooterLink>

        </FooterSection>
        <div className="mx-auto">
          <ReconLogoIcon />
        </div>
      </div>
      <div className="flex w-full flex-row items-center justify-between p-5 border-t border-stroke-neutral-decorative">
        <Link
          href="/"
          className="transition-all duration-200 hover:underline hover:text-accent-primary"
        >
          <Attribution color="primary">Recon</Attribution>
        </Link>
        <Link
          href="/privacy"
          className="transition-all duration-200 hover:underline hover:text-accent-primary"
        >
          <Attribution color="primary">Privacy Policy</Attribution>
        </Link>
        <Link
          href="/terms"
          className="transition-all duration-200 hover:underline hover:text-accent-primary"
        >
          <Attribution color="primary">Terms of Service</Attribution>
        </Link>
        <Attribution color="secondary">
          © 2024 Recon Fuzz. All rights reserved.
        </Attribution>
        <Image
          src="/recon-logo.svg"
          alt="Recon Logo"
          width={70}
          height={60}
          priority
        />
      </div>
    </div>
  );
}
